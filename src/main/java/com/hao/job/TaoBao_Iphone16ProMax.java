package com.hao.job;

import cn.hutool.core.date.TimeInterval;
import com.benjaminwan.ocrlibrary.OcrResult;
import com.benjaminwan.ocrlibrary.TextBlock;
import com.hao.entity.MatInferenceEngine;
import com.hao.entity.RecognitionResult;
import com.hao.entity.Region;
import com.hao.utils.ImageRecognitionTool;
import com.hao.utils.ResourceManager;
import com.hao.utils.ResourceUtils;
import com.hao.utils.WinRobotUtils;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Rect;

import java.awt.*;
import java.util.*;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 淘宝抢购自动化 - iPhone 16 Pro Max
 *
 * @auther: Yan Hao
 * @date: 2025/7/6
 */
public class TaoBao_Iphone16ProMax {
    // 配置参数
    private static final int MAX_RETURN_ATTEMPTS = 2;        // 最大返回尝试次数
    private static final int CLICK_RANDOM_OFFSET = 2;        // 点击随机偏移像素
    private static final int RETURN_RANDOM_OFFSET = 2;       // 返回操作偏移像素
    private static final int REFRESH_INTERVAL_MIN = 2;       // 刷新最小间隔(秒)
    private static final int REFRESH_INTERVAL_MAX = 60;      // 刷新最大间隔(秒)
    private static final int TAB_CLICK_INTERVAL_MIN = 300;   // 标签点击最小间隔(毫秒)
    private static final int TAB_CLICK_INTERVAL_MAX = 900;  // 标签点击最大间隔(毫秒)
    private static final int PAY_TIMEOUT = 2000;             // 支付识别超时(毫秒)
    private static final int ERROR_CHECK_TIMEOUT = 2000;     // 错误检查超时(毫秒)
    private static final int GRAB_BUTTON_Y_EXTEND = 20;      // 抢购按钮Y轴扩展像素
    private static final int SCAN_INTERVAL = 33;            // 扫描间隔(毫秒)
    private static final int MAX_QUANTITY_CLICK_MULTIPLIER = 2; // 最大数量点击倍数
    private static final int AFTER_PAY_WAIT = 1000;         // 支付后等待时间(毫秒)
    private static final boolean DIRECT_PURCHASE = false;    // 全局直接抢购开关（跳过数量调整）

    // 区域定义
    private static Region phoneScreenRegion;      // 手机投屏区域
    private static Region productListRegion;      // 商品列表区域
    private static Point allTabPoint;             // "全部"标签坐标
    private static Point newTabPoint;             // "新品"标签坐标
    private static Region paymentRegion;          // 支付区域
    private static Region actualPayRegion;        // 实付区域
    private static Region randomRegion;           // 随机区域
    private static Region quantityRegion;         // 商品数量区域
    private static Point returnPoint;             // 返回按钮坐标

    // 识别模板
    private static Map<String, Mat> productTemplates;        // 商品图片模板集合 key格式: name-quantity-needRandom-directPurchase
    private static Map<String, Mat> grabButtonTemplates;     // "马上抢"按钮模板
    private static Map<String, Mat> plusButtonTemplates;     // "+"号按钮模板
    private static Map<String, Mat> actualPayTemplates;      // 实付模板
    private static Map<String, Mat> payButtonTemplates;      // 支付按钮模板
    private static Map<String, Mat> errorButtonTemplates;    // 错误按钮模板(我知道了/返回)
    private static Map<String, Map<String, Mat>> baseScreenTemplates; // 基准屏幕确认模板

    // 运行状态
    private static boolean isRunning = true;
    private static int successCount = 0;
    private static long lastRefreshTime = 0;      // 上次刷新时间
    private static long nextRefreshInterval = 0;  // 下次刷新间隔

    public static void main(String[] args) throws Exception {
        job();
    }

    private static void job() throws Exception {
        initConfig();
        startGrabJob();
    }

    /**
     * 初始化配置
     */
    private static void initConfig() throws Exception {
        // 初始化区域
        phoneScreenRegion = new Region(120, 0, 701, 1525);  // 手机投屏区域
        productListRegion = new Region(124, 556, 229, 735);  // 商品列表区域
        paymentRegion = new Region(154, 1360, 643, 139);      // 支付区域
        actualPayRegion = new Region(320, 332, 97, 65);     // 实付区域
        randomRegion = new Region(168, 598, 203, 200);       // 随机选择区域
        quantityRegion = new Region(314, 458, 207, 63);      // 商品数量区域

        // 初始化坐标点
        allTabPoint = new Point(176, 534);        // "全部"标签位置
        newTabPoint = new Point(228, 524);        // "新品"标签位置
        returnPoint = new Point(162, 126);          // 返回按钮位置

        // 初始化模板
        productTemplates = new HashMap<>();
        grabButtonTemplates = new HashMap<>();
        plusButtonTemplates = new HashMap<>();
        actualPayTemplates = new HashMap<>();
        payButtonTemplates = new HashMap<>();
        errorButtonTemplates = new HashMap<>();
        baseScreenTemplates = new HashMap<>();

        // 加载商品模板 (name-quantity-needRandom-directPurchase)
        productTemplates = ResourceUtils.loadAllImagesFromDirectory("E:\\javaCVAll\\imgages\\taobao\\labubu_find");

        // 加载抢购按钮模板
        grabButtonTemplates.put("mscq", ImageRecognitionTool.loadImage("E:\\javaCVAll\\imgages\\taobao\\msq.png"));

        // 加载加号模板
        plusButtonTemplates.put("plus", ImageRecognitionTool.loadImage("E:\\javaCVAll\\imgages\\taobao\\plus.png"));

        // 加载实付模板
        actualPayTemplates.put("shifu", ImageRecognitionTool.loadImage("E:\\javaCVAll\\imgages\\taobao\\sf.png"));

        // 加载支付按钮模板
        payButtonTemplates.put("pay", ImageRecognitionTool.loadImage("E:\\javaCVAll\\imgages\\taobao\\pay.png"));

        // 加载错误按钮模板("我知道了"和"返回"按钮)
        errorButtonTemplates.put("know", ImageRecognitionTool.loadImage("E:\\javaCVAll\\imgages\\taobao\\fail1.png"));
        errorButtonTemplates.put("back", ImageRecognitionTool.loadImage("E:\\javaCVAll\\imgages\\taobao\\fail2.png"));

        // 加载基准屏幕确认模板
        Map<String, Mat> productListTemplates = new HashMap<>();
        productListTemplates.put("allTab", ImageRecognitionTool.loadImage("E:\\javaCVAll\\imgages\\taobao\\jz.png"));
        baseScreenTemplates.put("productList", productListTemplates);

        // 初始化显示边框
        phoneScreenRegion.setShowRegionBorder(true);

        // 初始化刷新时间
        lastRefreshTime = System.currentTimeMillis();
        nextRefreshInterval = getRandomRefreshInterval();

        System.out.println("配置初始化完成");
    }

    /**
     * 启动抢购任务
     */
    private static void startGrabJob() throws Exception {
        System.out.println("抢购任务开始，持续扫描商品...");

        while (isRunning) {
            try {
                // 检查是否需要刷新商品列表
                long currentTime = System.currentTimeMillis();
                if (currentTime - lastRefreshTime > nextRefreshInterval) {
                    System.out.println("触发定时刷新");
                    refreshProductList();
                    lastRefreshTime = currentTime;
                    nextRefreshInterval = getRandomRefreshInterval();
                    System.out.println("下次刷新间隔: " + (nextRefreshInterval / 1000) + "秒");
                }

                // 识别商品
                // TimeInterval interval = new TimeInterval();
                RecognitionResult productResult = findTargetProduct();
                // System.out.println("识别商品耗时: " + interval.interval() + "ms");

                if (productResult != null && productResult.isFound()) {
                    System.out.printf("找到目标商品 [%s] 置信度: %.2f 位置: (%d,%d)%n",
                            productResult.getTemplateId(),
                            productResult.getMatchScore(),
                            productResult.getCenterPoint().x(),
                            productResult.getCenterPoint().y());

                    // 识别并点击抢购按钮
                    boolean grabbed = findAndClickGrabButton(productResult);

                    if (grabbed) {
                        // 处理商品详情页
                        handleProductDetail(productResult.getTemplateId());
                    } else {
                        // 未找到抢购按钮，继续扫描
                        System.out.println("未找到抢购按钮，继续扫描");
                    }
                }

                // 短暂间隔，避免过高CPU占用
                Thread.sleep(SCAN_INTERVAL);

            } catch (Exception e) {
                System.err.println("抢购过程发生错误: " + e.getMessage());
                e.printStackTrace();
                // 尝试返回商品列表
                returnToProductList();
                Thread.sleep(500);
            }
        }

        System.out.println("抢购任务结束，成功购买: " + successCount + " 件商品");
    }

    /**
     * 获取随机刷新间隔时间(毫秒)
     */
    private static long getRandomRefreshInterval() {
        return ThreadLocalRandom.current().nextInt(REFRESH_INTERVAL_MIN, REFRESH_INTERVAL_MAX + 1) * 1000L;
    }

    /**
     * 获取随机Tab点击间隔时间(毫秒)
     */
    private static long getRandomTabClickInterval() {
        return ThreadLocalRandom.current().nextInt(TAB_CLICK_INTERVAL_MIN, TAB_CLICK_INTERVAL_MAX + 1);
    }

    /**
     * 寻找目标商品
     */
    private static RecognitionResult findTargetProduct() throws Exception {
        return productListRegion.find(productTemplates, 0.75);
    }

    /**
     * 刷新商品列表（点击全部和新品标签）
     */
    private static void refreshProductList() throws Exception {
        // 点击"全部"标签
        WinRobotUtils.randomOffsetClick(allTabPoint.x, allTabPoint.y, RETURN_RANDOM_OFFSET);
        Thread.sleep(getRandomTabClickInterval());

        // 点击"新品"标签
        WinRobotUtils.randomOffsetClick(newTabPoint.x, newTabPoint.y, RETURN_RANDOM_OFFSET);
        Thread.sleep(getRandomTabClickInterval());

        // 再点回"全部"标签
        WinRobotUtils.randomOffsetClick(allTabPoint.x, allTabPoint.y, RETURN_RANDOM_OFFSET);
        Thread.sleep(getRandomTabClickInterval());

        System.out.println("商品列表已刷新");
    }

    /**
     * 识别并点击"马上抢"按钮
     */
    private static boolean findAndClickGrabButton(RecognitionResult productResult) throws Exception {
        // 获取商品区域
        Rect productRect = productResult.getDetectionArea();

        // 确定抢购按钮识别区域（商品右侧到屏幕边缘，Y轴适当扩展）
        int startX = productRect.x() + productRect.width();
        int startY = Math.max(0, productRect.y() - GRAB_BUTTON_Y_EXTEND);
        int width = phoneScreenRegion.getWidth() - (startX - phoneScreenRegion.getX());
        int height = productRect.height() + GRAB_BUTTON_Y_EXTEND * 2;

        Region grabButtonRegion = new Region(startX, startY, width, height);

        // 识别抢购按钮
        RecognitionResult grabResult = grabButtonRegion.find(grabButtonTemplates, 0.75);

        if (grabResult != null && grabResult.isFound()) {
            System.out.printf("找到抢购按钮 [%s] 置信度: %.2f 位置: (%d,%d)%n",
                    grabResult.getTemplateId(),
                    grabResult.getMatchScore(),
                    grabResult.getCenterPoint().x(),
                    grabResult.getCenterPoint().y());

            // 点击抢购按钮
            WinRobotUtils.leftClick(
                    grabResult.getCenterPoint().x(),
                    grabResult.getCenterPoint().y());

            return true;
        }

        return false;
    }

    /**
     * 处理商品详情页
     */
    private static void handleProductDetail(String productId) throws Exception {
        // 等待页面加载，较短等待
        Thread.sleep(500);

        // 解析商品ID获取购买数量、是否需要随机选择和是否直接购买
        String[] parts = productId.split("-");
        int targetQuantity = 1; // 默认购买1个
        boolean needRandom = false; // 默认不需要随机选择
        boolean directPurchase = DIRECT_PURCHASE; // 默认使用全局配置
        
        // 如果ID包含数量和随机选择信息
        if (parts.length >= 2) {
            try {
                targetQuantity = Integer.parseInt(parts[1]);
            } catch (NumberFormatException e) {
                System.out.println("无法解析购买数量，使用默认值1");
            }
            
            if (parts.length >= 3) {
                needRandom = Boolean.parseBoolean(parts[2]);
                
                // 如果ID包含直接购买配置
                if (parts.length >= 4) {
                    directPurchase = Boolean.parseBoolean(parts[3]);
                }
            }
        }
        
        // 显示当前配置信息
        System.out.println("商品配置: 数量=" + targetQuantity + 
                ", 需要随机=" + needRandom + 
                ", 直接购买=" + directPurchase);
                
        // 直接购买模式下，先检查支付按钮
        if (directPurchase) {
            System.out.println("直接抢购模式，尝试直接查找支付按钮...");
            RecognitionResult payButtonResult = paymentRegion.wait(payButtonTemplates, 0.75, 1000);
            
            if (payButtonResult != null && payButtonResult.isFound()) {
                System.out.println("找到支付按钮，跳过数量调整，直接支付...");
                clickPayButton();
                return;
            } else {
                System.out.println("未找到支付按钮，继续检查实付信息...");
            }
        }

        // 检查是否存在"实付"图像
        RecognitionResult payResult = actualPayRegion.wait(actualPayTemplates, 0.75, 1000);
        
        // 如果找到实付信息，进行购买流程
        if (payResult != null && payResult.isFound()) {
            System.out.println("检测到实付信息，准备支付...");
            
            // 如果不是直接购买模式，则调整商品数量
            if (!directPurchase) {
                System.out.println("非直接抢购模式，调整商品数量...");
                adjustProductQuantity(targetQuantity);
            } else {
                System.out.println("直接抢购模式，跳过数量调整...");
            }
            
            // 点击支付按钮
            clickPayButton();
            return;
        }
        
        // 未找到实付信息，检查是否需要选择随机
        if (needRandom) {
            System.out.println("未检测到实付信息，检查是否需要随机选择...");
            boolean randomFound = handleRandomSelection();
            
            if (randomFound) {
                // 选择随机后，再次检查实付信息
                Thread.sleep(500);
                payResult = actualPayRegion.wait(actualPayTemplates, 0.75, 1000);
                
                if (payResult != null && payResult.isFound()) {
                    System.out.println("选择随机后检测到实付信息，准备支付...");
                    
                    // 如果不是直接购买模式，则调整商品数量
                    if (!directPurchase) {
                        System.out.println("非直接抢购模式，调整商品数量...");
                        adjustProductQuantity(targetQuantity);
                    } else {
                        System.out.println("直接抢购模式，跳过数量调整...");
                    }
                    
                    // 点击支付按钮
                    clickPayButton();
                    return;
                } else {
                    // 如果直接购买模式，尝试再次直接查找支付按钮
                    if (directPurchase) {
                        System.out.println("直接抢购模式，选择随机后再次尝试直接查找支付按钮...");
                        RecognitionResult payButtonResult = paymentRegion.wait(payButtonTemplates, 0.75, 1000);
                        
                        if (payButtonResult != null && payButtonResult.isFound()) {
                            System.out.println("找到支付按钮，直接支付...");
                            clickPayButton();
                            return;
                        }
                    }
                    System.out.println("选择随机后仍未检测到实付信息或支付按钮，返回商品列表");
                }
            } else {
                System.out.println("未找到随机选项，返回商品列表");
            }
        } else {
            System.out.println("未检测到实付信息且不需要随机选择，返回商品列表");
        }
        
        // 如果流程走到这里，说明无法完成购买，返回商品列表
        returnToProductList();
    }

    /**
     * 处理随机选择
     */
    private static boolean handleRandomSelection() throws Exception {
        RecognitionResult randomResult = randomRegion.waitText("随机", 0.7, 1000); // 缩短超时时间

        if (randomResult != null && randomResult.isFound()) {
            System.out.println("检测到随机选项，点击...");

            WinRobotUtils.randomOffsetClick(
                    randomResult.getCenterPoint().x(),
                    randomResult.getCenterPoint().y(),
                    CLICK_RANDOM_OFFSET);

            // 等待选择生效，较短等待
            Thread.sleep(300);
            return true;
        }

        return false;
    }

    /**
     * 调整商品数量 - 仅使用OCR验证
     */
    private static void adjustProductQuantity(int targetQuantity) throws Exception {
        if (targetQuantity <= 1) {
            return; // 默认数量为1，无需调整
        }

        // 识别加号按钮
        RecognitionResult plusResult = quantityRegion.find(plusButtonTemplates, 0.75);

        if (plusResult != null && plusResult.isFound()) {
            System.out.println("找到加号按钮，准备调整数量到: " + targetQuantity);

            // 当前数量初始为1
            int currentQuantity = 1;
            int clickCount = 0;
            int maxClicks = targetQuantity * MAX_QUANTITY_CLICK_MULTIPLIER; // 最大点击次数为目标的2倍
            boolean quantityVerified = false;

            // 循环点击加号直到达到目标数量
            while (clickCount < maxClicks && !quantityVerified) {
                // 点击加号（不使用随机偏移）
                WinRobotUtils.leftClick(
                        plusResult.getCenterPoint().x(),
                        plusResult.getCenterPoint().y());

                Thread.sleep(300); // 等待时间
                currentQuantity++;
                clickCount++;

                // 每点击一定次数后验证数量
                if (currentQuantity >= targetQuantity || clickCount % 3 == 0) {
                    // 使用OCR识别当前数量
                    int recognizedQuantity = recognizeCurrentQuantity();

                    if (recognizedQuantity == targetQuantity) {
                        System.out.println("OCR验证数量成功: " + recognizedQuantity);
                        quantityVerified = true;
                        break;
                    } else if (recognizedQuantity > 0) {
                        // OCR识别到数量但不匹配目标
                        currentQuantity = recognizedQuantity;
                        System.out.println("当前数量: " + currentQuantity + "，目标数量: " + targetQuantity);
                    }
                }
            }

            if (quantityVerified) {
                System.out.println("商品数量已调整为: " + targetQuantity);
            } else {
                System.out.println("无法验证商品数量，已点击 " + clickCount + " 次，继续后续流程");
            }
        } else {
            System.out.println("未找到加号按钮，无法调整数量");
        }
    }

    /**
     * OCR识别当前商品数量
     */
    private static int recognizeCurrentQuantity() {
        try {
            // 获取区域截图
            Mat regionMat = ResourceManager.getInstance().getScreenMonitor().getRegionFrame(quantityRegion.getRectangle());

            // OCR识别
            MatInferenceEngine ocr = ResourceManager.getInstance().getMatInferenceEngine();
            OcrResult ocrResult = ocr.runOcr(regionMat);

            // 释放资源
            if (regionMat != null) {
                regionMat.close();
            }

            // 解析OCR结果
            for (TextBlock block : ocrResult.getTextBlocks()) {
                String text = block.getText().trim();
                if (text.matches("\\d+")) {
                    return Integer.parseInt(text);
                }
            }
        } catch (Exception e) {
            System.err.println("OCR识别数量失败: " + e.getMessage());
        }

        return -1; // 识别失败返回-1
    }

    /**
     * 点击支付按钮并处理可能的错误情况
     */
    private static boolean clickPayButton() throws Exception {
        // 使用图片匹配识别支付按钮
        RecognitionResult payButtonResult = paymentRegion.wait(payButtonTemplates, 0.75, PAY_TIMEOUT);

        if (payButtonResult != null && payButtonResult.isFound()) {
            System.out.println("找到支付按钮，点击...");
            System.out.println("支付按钮位置: " + payButtonResult.getCenterPoint().x() + ", " + payButtonResult.getCenterPoint().y());

            // 点击支付按钮
            WinRobotUtils.leftClick(
                    payButtonResult.getCenterPoint().x(),
                    payButtonResult.getCenterPoint().y());

            // 短暂等待支付响应
            Thread.sleep(300);

            // 检查是否出现错误提示("我知道了"或"返回"按钮)
            System.out.println("检查支付是否出现错误...");
            RecognitionResult errorResult = paymentRegion.wait(errorButtonTemplates, 0.75, ERROR_CHECK_TIMEOUT);

            if (errorResult != null && errorResult.isFound()) {
                // 检测到错误按钮
                System.out.println("检测到错误按钮: " + errorResult.getTemplateId() +
                        "，位置: (" + errorResult.getCenterPoint().x() + "," + errorResult.getCenterPoint().y() + ")");

                // 点击错误按钮（我知道了或返回）
                WinRobotUtils.leftClick(
                        errorResult.getCenterPoint().x(),
                        errorResult.getCenterPoint().y());

                System.out.println("支付失败，返回商品列表...");
                Thread.sleep(500);
                returnToProductList();
                return false;
            } else {
                // 未检测到错误按钮，但也可能是超时未响应，同样执行返回
                if (isInProductList()) {
                    // 如果已经回到了商品列表（说明支付成功）
                    successCount++;
                    System.out.println("支付成功! 总成功次数: " + successCount);
                    return true;
                } else {
                    // 如果未回到商品列表，说明可能卡在支付页面
                    System.out.println("支付超时未响应，执行返回操作...");
                    returnToProductList();
                    return false;
                }
            }
        } else {
            // 检查是否有网络异常等错误
            List<String> errorTexts = Arrays.asList("网络异常", "失败", "错误");
            for (String errorText : errorTexts) {
                RecognitionResult errorResult = paymentRegion.waitText(errorText, 0.7, 500);
                if (errorResult != null && errorResult.isFound()) {
                    System.out.println("检测到错误: " + errorText);
                    returnToProductList();
                    return false;
                }
            }

            System.out.println("未找到支付按钮，返回商品列表");
            returnToProductList();
            return false;
        }
    }

    /**
     * 返回商品列表并确认
     */
    private static boolean returnToProductListAndVerify() throws Exception {
        // 可能需要多次点击返回
        for (int i = 0; i < MAX_RETURN_ATTEMPTS * 2; i++) { // 增加尝试次数
            // 点击返回按钮
            WinRobotUtils.randomOffsetClick(
                    returnPoint.x,
                    returnPoint.y,
                    RETURN_RANDOM_OFFSET);

            Thread.sleep(700); // 短暂等待

            // 检查是否已返回商品列表
            if (isInProductList()) {
                System.out.println("已成功确认返回到商品列表");
                return true;
            }

            // 多等待一段时间
            Thread.sleep(300);
        }

        System.out.println("未能确认返回到商品列表，将继续尝试");
        return false;
    }

    /**
     * 返回商品列表
     */
    private static void returnToProductList() throws Exception {
        // 可能需要多次点击返回
        for (int i = 0; i < MAX_RETURN_ATTEMPTS; i++) {
            // 点击返回按钮
            WinRobotUtils.randomOffsetClick(
                    returnPoint.x,
                    returnPoint.y,
                    RETURN_RANDOM_OFFSET);

            Thread.sleep(500); // 减短等待时间

            // 检查是否已返回商品列表
            if (isInProductList()) {
                System.out.println("已返回商品列表");
                break;
            }
        }
    }

    /**
     * 检查是否在商品列表页面
     */
    private static boolean isInProductList() throws Exception {
        // 在预定义区域内查找"全部"标签
        Map<String, Mat> baseTemplates = baseScreenTemplates.get("productList");
        if (baseTemplates != null) {
            Region checkRegion = new Region(122, 434, 117, 79);
            RecognitionResult result = checkRegion.find(baseTemplates, 0.7);
            return result != null && result.isFound();
        }
        return false;
    }
}
